# Dependency directories
/node_modules
/.pnp
.pnp.js

# Production build directory
/build
/dist

# Development server artifacts
/.docusaurus
/.cache
/.yarn

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln

# OS-generated files
.DS_Store
Thumbs.db

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# Compiled binary addons (like node-sass)
/build/Release

# Output of 'npm pack'
*.tgz

# Next.js
/.next/
/out/

# User-specific files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# TypeScript
*.tsbuildinfo

# ESLint
.eslintcache

# Jest
/coverage/
/.jest/

# Cypress
/cypress/videos/
/cypress/screenshots/

# Prettier
/prettier.config.js

# Visual Studio Code
.vscode/
.history/
*.code-workspace

# Yarn
.yarn/*
!.yarn/patches
!.yarn/releases
!.yarn/plugins
!.yarn/sdks

# Mac
.DS_Store

# JetBrains
.idea/

# WebStorm
.idea/

# Windows
Thumbs.db
