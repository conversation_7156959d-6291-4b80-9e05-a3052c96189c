import React from 'react';

const DesignIcon = ({ fill }) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" style={{ fill: "var(--svg-fill-color)", }} width="25px" height="25px" viewBox="0 0 54.532 54.532">
            <defs>
                <style>

                    {`
        :root {
            --svg-fill-color: none;
            --svg-stroke-color: none;
          }
        .cls-1 {
          fill: none;
          stroke-linecap: round;
          stroke-linejoin: round;
        }`}
                </style>
            </defs>
            <path id="designing_ruller" data-name="designing ruller" d="M176.258,251l.657-.652.656.655,1.361,1.359.66.657-.659.66-4.332,4.333,2.123,2.123,1.945-1.945.659-.659.656.659,1.362,1.357.659.658-.658.659-1.949,1.947,2.126,2.126,1.944-1.948.657-.657.659.657,1.361,1.36.66.656-.66.659-1.945,1.947,1.635,1.634,10.177-10.177L180.835,243.89a1.3,1.3,0,0,0-1.824,0l-8.352,8.352a1.3,1.3,0,0,0,0,1.825l1.266,1.264,4.167-4.168Zm37.61,13.426-9.961-9.96-6.794,6.794-10.621,10.62-10.942,10.94.482.329,1.8,1.228.188.128.108.2.1.178,1.82,3.384,3.562,1.917.2.108.128.188,1.166,1.713.4.591,10.946-10.945,12.152-12.152Zm10.574,23.072-15.176-15.178L199.087,282.5l1.635,1.634,1.946-1.946.658-.661.659.662,1.359,1.362.657.657-.657.658-1.946,1.945,2.124,2.124,1.947-1.947.659-.657.658.657,1.358,1.36.659.658-.659.658-1.947,1.946,2.124,2.124,4.167-4.169.169-.167.658-.653.656.656,1.359,1.359.657.66-.657.658L213,296.408l1.265,1.264a1.3,1.3,0,0,0,1.826,0l4.176-4.175,4.175-4.179a1.3,1.3,0,0,0,0-1.823Zm-45.867,2.62-.246-.132-.132-.245-.37-.687-1.584-2.947-.977-.664-.672-.459-.849,5.269,4.338,4.335,5.26-.846-1.137-1.669ZM222.05,256.24a1.3,1.3,0,0,0,0-1.823l-8.138-8.136a1.3,1.3,0,0,0-1.823,0l-4.564,4.565,9.96,9.961Zm-6.211,6.212-9.961-9.96-.325.325,9.961,9.96ZM173.122,294.12l-.207,1.3,2.33-.373-1.955-1.957Z" transform="translate(-170.284 -243.515)" />
        </svg>
    );
};

export default DesignIcon;
