var _excluded = ["title", "titleId"];
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
function _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }
function _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }
import * as React from "react";
function SvgSeniors(_ref, svgRef) {
  var title = _ref.title,
    titleId = _ref.titleId,
    props = _objectWithoutProperties(_ref, _excluded);
  return /*#__PURE__*/React.createElement("svg", _extends({
    "data-name": "Layer 1",
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 190 173.22",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title === undefined ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, "seniors") : title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("path", {
    d: "M70.41,130.44a6.79,6.79,0,0,0-2-2.61,12.3,12.3,0,0,0-4.41-2.3,21.74,21.74,0,0,0-6.42-.86H57a32.2,32.2,0,0,0-9.16,1.7l-16.54,5.34,11.11-13.36.91-1.08a33.61,33.61,0,0,1-8.14,1.5c-.71,0-1.44.08-2.17.08a20.07,20.07,0,0,1-9.23-2,16.8,16.8,0,0,1-7.2-6.7,18,18,0,0,1-2.3-9,21.67,21.67,0,0,1,.27-3.41,12.59,12.59,0,0,1-1.74-.49A17.56,17.56,0,0,1,8.3,94.94a20.69,20.69,0,0,1-3.89-3.57,19,19,0,0,1-3-4.83,16.88,16.88,0,0,1-1.36-6,15.54,15.54,0,0,1,1.27-6.82,17,17,0,0,1,5.29-6.93,25.46,25.46,0,0,1,6.72-3.7,39.61,39.61,0,0,1,7.23-1.9c2.35-.39,4.65-.7,6.85-.92l6.12-.6,1.07-.12a22.81,22.81,0,0,1,1.54-3.24,25.24,25.24,0,0,1,4.63-5.89A28,28,0,0,1,47.05,46a32.26,32.26,0,0,1,7.63-2.79,21.75,21.75,0,0,1,4.74-.56,16,16,0,0,1,4.57.64,15.2,15.2,0,0,1,7,4.27,14.67,14.67,0,0,1,3.57,7.06,16.28,16.28,0,0,1-.3,7.78,17.18,17.18,0,0,1-2,4.51,18.11,18.11,0,0,1,3.31-.3,10.91,10.91,0,0,1,5,1.12l2-.49L83.62,67a11.84,11.84,0,0,1,1.67-.29l.27,0a12.72,12.72,0,0,1-.62-2.07l-.72-3.35L93,54.35l3,5.5a1.39,1.39,0,0,0,.29.15h0l.11,0c.32,0,.63,0,.95,0A10.34,10.34,0,0,0,98.63,60a23,23,0,0,0,2.67-.57c.9-.25,1.79-.54,2.65-.87,1-.4,2-.78,3-1.12s2.19-.72,3.19-1a14,14,0,0,1,3.36-.42h.73a12,12,0,0,1,4.16,1,11.15,11.15,0,0,1,3.3,2.18A10.53,10.53,0,0,1,124,62.38a9.31,9.31,0,0,1,.86,3.8,8.33,8.33,0,0,1-.14,1.66,18,18,0,0,1,6.59-1.23,11.71,11.71,0,0,1,6.33,1.71l.2.13a12.06,12.06,0,0,1,3.1-3.88,9.91,9.91,0,0,1,6.4-2.34,7.19,7.19,0,0,1,7,4.52,8.14,8.14,0,0,1,.58,3.25,12.32,12.32,0,0,1,3-3.63A10,10,0,0,1,164.28,64a7.13,7.13,0,0,1,7,4.61,8.72,8.72,0,0,1,.49,3.92,10.39,10.39,0,0,1,2.55,2,10,10,0,0,1,1.84,2.85,12.1,12.1,0,0,1,1.89.26,14,14,0,0,1,4.45,1.84l.15.1a16.66,16.66,0,0,1,6,7.57,18.68,18.68,0,0,1,1.13,9.72,23.42,23.42,0,0,1-3.9,9.68,28.19,28.19,0,0,1-8.94,8.26A45.12,45.12,0,0,1,163.27,120,61.35,61.35,0,0,1,150,121.33c-1.77,0-3.64-.06-5.55-.18-5-.27-10-.49-14.84-.65-2.72-.09-5.43-.13-8-.13-2,0-3.91,0-5.79.07-4.35.12-8.52.41-12.42.87A62.62,62.62,0,0,0,93,123.39a36.66,36.66,0,0,0-8.23,3.49,20.63,20.63,0,0,0-5.67,4.87l-5.47,6.83Zm-41-27.85a3.38,3.38,0,0,0,.6.72,4.37,4.37,0,0,0,1.61.92,5.23,5.23,0,0,0,1.76.26l.78,0a10.9,10.9,0,0,0,3.93-1.08A9.11,9.11,0,0,0,42.74,98a11.19,11.19,0,0,0,.51-4.08,16.11,16.11,0,0,0-1.16-5,15.57,15.57,0,0,0-.73-1.64,11.73,11.73,0,0,1,0,3.65,14.1,14.1,0,0,1-1.58,4.71,14.4,14.4,0,0,1-3.35,4.1,12.5,12.5,0,0,1-5.4,2.68A10.2,10.2,0,0,1,29.4,102.59ZM62,99.27a25.79,25.79,0,0,1-1.16,3c-.12.27-.26.54-.39.81l1.25-.68c1.36-.71,2.76-1.36,4.2-2A13.55,13.55,0,0,1,62,99.27Zm66,.16c2.87.56,5.77,1.07,8.64,1.55,3.41.55,6.95,1,10.51,1.33,2.57.24,5.33.36,8.19.36,1,0,2.07,0,3.12,0a28.7,28.7,0,0,0,7.65-1.12A21.18,21.18,0,0,0,171.64,99a12.78,12.78,0,0,0,3.27-3.07,9.13,9.13,0,0,0,1.48-3,3.56,3.56,0,0,0,0-1.89,2.31,2.31,0,0,0-.79-1.15,1.4,1.4,0,0,0-.32-.22l-.12.11a19.72,19.72,0,0,1-1.81,3.13,17.8,17.8,0,0,1-4.62,4.51,18.09,18.09,0,0,1-5.58,2.45,23.46,23.46,0,0,1-5.72.69,9.81,9.81,0,0,1-5.95-1.79,16.07,16.07,0,0,1-2.12.94,13.34,13.34,0,0,1-4.65.85,11,11,0,0,1-4.59-.93,8.18,8.18,0,0,1-3.92-3.67,8.39,8.39,0,0,1-.63-1.55,25.61,25.61,0,0,1-2,1.82,19.42,19.42,0,0,1-5.44,3.19ZM33.78,73.5l-.69.12a29.11,29.11,0,0,0-5.26,1.5,19.51,19.51,0,0,0-4.24,2.31,11.22,11.22,0,0,0-2.77,2.86,4.33,4.33,0,0,0-.71,1.8,4.43,4.43,0,0,0,.13,1.75,2.83,2.83,0,0,0,.6,1.07,1.49,1.49,0,0,0,.23.2l.31-.3a19.06,19.06,0,0,1,8.89-4.65,11,11,0,0,1,2.52-.3,8.57,8.57,0,0,1,4.1,1l.32.18a27,27,0,0,1-2.82-5.57C34.16,74.81,34,74.15,33.78,73.5Zm24.91,2.28.33.53.46-.7C59.22,75.68,59,75.73,58.69,75.78Z",
    transform: "translate(0 0)",
    className: "cjd-fill",
    style: {
      fill: "#525a6f"
    }
  }), /*#__PURE__*/React.createElement("path", {
    d: "M186.33,88.14a14.07,14.07,0,0,0-5.14-6.44l-.07,0a11.35,11.35,0,0,0-3.64-1.5,8.81,8.81,0,0,0-2-.22,7.71,7.71,0,0,0-1.18.08,8.12,8.12,0,0,0-1.89-3.73,8,8,0,0,0-3.57-2.21,6.92,6.92,0,0,0,0-4.54,4.64,4.64,0,0,0-4.61-3,7.57,7.57,0,0,0-4.87,1.77,10.92,10.92,0,0,0-3.12,4.51v0a7.14,7.14,0,0,0-.49,2.83,6.51,6.51,0,0,0,.55,2.52,5.32,5.32,0,0,0,.53.9,9.08,9.08,0,0,0-1.28.79,12.06,12.06,0,0,0-2.05,2,15.11,15.11,0,0,0-1.53,2.2c.19-.44.38-.9.59-1.38l.86-2.09c1-2.51,1.2-4.48.51-6a4.32,4.32,0,0,0-2.13-2.18,6.58,6.58,0,0,0,.08-4.72,4.69,4.69,0,0,0-4.62-2.94,7.49,7.49,0,0,0-4.81,1.77A10.42,10.42,0,0,0,139.44,71a9.21,9.21,0,0,0-.63,2.12,7.79,7.79,0,0,0-2.56-2.61,9.12,9.12,0,0,0-5-1.32,15.44,15.44,0,0,0-10,3.66A21,21,0,0,0,116.78,78a8.07,8.07,0,0,0-.07-5.47.67.67,0,0,0-.06-.12,6.37,6.37,0,0,0,2.87-1,5.72,5.72,0,0,0,2.15-2.4,6.33,6.33,0,0,0,.63-2.81,7,7,0,0,0-.64-2.8A7.94,7.94,0,0,0,120,61a8.64,8.64,0,0,0-2.56-1.69,9.75,9.75,0,0,0-3.31-.77h-.56a11.54,11.54,0,0,0-2.76.35c-.92.22-1.91.53-3,.89s-2,.72-3,1.11-1.91.67-2.87.94a24.79,24.79,0,0,1-3,.63,10.85,10.85,0,0,1-1.65.12q-.6,0-1.2-.06a2.73,2.73,0,0,1-.83-.2,4.08,4.08,0,0,1-.78-.43,2.58,2.58,0,0,1-.41-.34,1.84,1.84,0,0,1-.23-.31l-1.68-3.07L87,62.26l.39,1.82a10.32,10.32,0,0,0,1.46,3.54A10.53,10.53,0,0,0,89.92,69a9.11,9.11,0,0,0,.9.85A9.12,9.12,0,0,0,87,69.14a13.88,13.88,0,0,0-1.44.07,9.15,9.15,0,0,0-1.36.24l-1,.24L82,70l-.68,1.45-.24-.25a7.4,7.4,0,0,0-5.52-2,15.71,15.71,0,0,0-10,3.58A22.5,22.5,0,0,0,59,81.81a29.15,29.15,0,0,0-5-8.14l-.34-.4.78.14a9.49,9.49,0,0,0,1.53.12,15.63,15.63,0,0,0,5.62-1.2,17,17,0,0,0,6.8-4.49,15.54,15.54,0,0,0,3.48-6.12,13.76,13.76,0,0,0,.26-6.59,12.17,12.17,0,0,0-3-5.86,12.78,12.78,0,0,0-5.86-3.56,13.54,13.54,0,0,0-3.86-.54,18.6,18.6,0,0,0-4.17.51,29,29,0,0,0-7,2.56,25.57,25.57,0,0,0-5.75,4.06,22.89,22.89,0,0,0-4.18,5.3,21.18,21.18,0,0,0-1.84,4.25,27.19,27.19,0,0,1-2.72.37l-6.1.6c-2.15.22-4.4.52-6.69.9a37.61,37.61,0,0,0-6.77,1.78,23.09,23.09,0,0,0-6.06,3.33,14.53,14.53,0,0,0-4.5,5.91,13.13,13.13,0,0,0-1.08,5.73,14.37,14.37,0,0,0,1.16,5.07,16.84,16.84,0,0,0,2.63,4.2,19.11,19.11,0,0,0,3.43,3.15,15.92,15.92,0,0,0,3.84,2,9.88,9.88,0,0,0,3.17.58,7.62,7.62,0,0,0,.85,0,20,20,0,0,0-.85,5.76,15.55,15.55,0,0,0,2,7.74,14.42,14.42,0,0,0,6.15,5.69A17.73,17.73,0,0,0,33,116.34c.66,0,1.34,0,2-.07a30,30,0,0,0,10.16-2.37,27.74,27.74,0,0,0,8-5.25,24.77,24.77,0,0,0,5.4-7.48,23.94,23.94,0,0,0,1.87-6,9.55,9.55,0,0,0,2.23,1.61A11.65,11.65,0,0,0,68.15,98,21.75,21.75,0,0,0,77.78,96l.32-.16C79.07,97,80.82,98,84,98a14.2,14.2,0,0,0,2.71-.23h.06l1.08-.26L89,97.22l.69-1.48a6,6,0,0,0,2.09,1.56,8.47,8.47,0,0,0,3.54.71,10.78,10.78,0,0,0,3.78-.7A14.75,14.75,0,0,0,101.82,96a7.12,7.12,0,0,0,5.39,2,10.78,10.78,0,0,0,3.78-.7,14.09,14.09,0,0,0,3.33-1.74c.26-.17.51-.36.76-.56a8.06,8.06,0,0,0,1.86,1.67A9.15,9.15,0,0,0,122,98a14.87,14.87,0,0,0,5.31-1A16.79,16.79,0,0,0,132,94.25,22,22,0,0,0,135.9,90c.23-.32.45-.64.66-1a4.35,4.35,0,0,0,1.28-.91l.66-.67a11.52,11.52,0,0,0-.81,3.6,7.15,7.15,0,0,0,.74,3.69,5.71,5.71,0,0,0,2.74,2.55,8.47,8.47,0,0,0,3.54.71,11,11,0,0,0,3.78-.7,14.88,14.88,0,0,0,3.31-1.72,6.29,6.29,0,0,0,1,1A7.25,7.25,0,0,0,157.43,98a21,21,0,0,0,5.11-.62,15.51,15.51,0,0,0,4.8-2.11,15,15,0,0,0,4-3.88,17.2,17.2,0,0,0,1.71-3,3.32,3.32,0,0,1,1.58-1.18,2.45,2.45,0,0,1,.69-.1,3.12,3.12,0,0,1,1.92.81,4.84,4.84,0,0,1,1.61,2.39,6,6,0,0,1,0,3.19,11.32,11.32,0,0,1-1.88,3.9,14.84,14.84,0,0,1-3.92,3.69,23.37,23.37,0,0,1-6.15,2.84,30.75,30.75,0,0,1-8.32,1.23c-1.08,0-2.15,0-3.19,0-2.94,0-5.77-.12-8.42-.37-3.62-.33-7.22-.79-10.69-1.35s-7-1.22-10.5-1.91-7.29-1.35-11.14-1.91-8-1-12.34-1.31c-2.62-.18-5.42-.28-8.32-.28-1.93,0-3.94,0-6,.13a63.8,63.8,0,0,0-13.4,1.94,54.54,54.54,0,0,0-21.69,11.19A68.08,68.08,0,0,0,44.3,120l-5.56,6.68L47,124a34.51,34.51,0,0,1,9.89-1.83h.65a24.39,24.39,0,0,1,7.17,1,15,15,0,0,1,5.31,2.8,9.48,9.48,0,0,1,2.72,3.6l1.58,4.08,2.74-3.42a23.06,23.06,0,0,1,6.36-5.46A39,39,0,0,1,92.22,121a66.59,66.59,0,0,1,10.87-2.16c4-.47,8.21-.77,12.64-.88,1.91-.05,3.88-.08,5.85-.08,2.64,0,5.37,0,8.13.13,4.85.16,9.87.38,14.9.66,1.86.11,3.68.17,5.4.17a59.5,59.5,0,0,0,12.7-1.24,42.72,42.72,0,0,0,12.92-4.87,25.66,25.66,0,0,0,8.16-7.5,21,21,0,0,0,3.5-8.64A16,16,0,0,0,186.33,88.14Zm-130-24.56a5.48,5.48,0,0,1,.5-1.89,6.64,6.64,0,0,1,1.25-1.85,3.57,3.57,0,0,1,1.4-1l3.16-1.12a5.19,5.19,0,0,1-.21,1.82A6.05,6.05,0,0,1,61,62a7.17,7.17,0,0,1-3,1.84,5,5,0,0,1-1.66.39h0A2.28,2.28,0,0,1,56.37,63.58ZM45.13,98.77a12.1,12.1,0,0,1-2.26,4,11.8,11.8,0,0,1-3.64,2.83A13.24,13.24,0,0,1,34.35,107c-.36,0-.69,0-1,0a7.89,7.89,0,0,1-2.6-.4,7.07,7.07,0,0,1-2.49-1.46,5.78,5.78,0,0,1-1.44-2.08,6.88,6.88,0,0,1-.54-2.6,5.88,5.88,0,0,1,.07-1,5.45,5.45,0,0,0,2.61.64,6.6,6.6,0,0,0,1.53-.18,10,10,0,0,0,4.33-2.14,12,12,0,0,0,2.76-3.38,11.75,11.75,0,0,0,1.31-3.87,8.68,8.68,0,0,0-.31-4,6,6,0,0,0-2.89-3.45,6.1,6.1,0,0,0-2.91-.69,8.45,8.45,0,0,0-1.95.24,16.59,16.59,0,0,0-7.74,4q-.68.65-1.29,1.32l-1.38-.41A3.24,3.24,0,0,1,19,86.64a5.29,5.29,0,0,1-1.15-2,6.59,6.59,0,0,1,.87-5.72,13.5,13.5,0,0,1,3.4-3.51,22,22,0,0,1,4.79-2.62,30.71,30.71,0,0,1,5.72-1.62c1-.2,2.08-.36,3.14-.48a20.77,20.77,0,0,0,1,3.93,26.1,26.1,0,0,0,4,7.08A25.13,25.13,0,0,1,44.44,88a18.38,18.38,0,0,1,1.32,5.81A13.65,13.65,0,0,1,45.13,98.77ZM78.49,85.94a8.76,8.76,0,0,1-1.06,1.17,9.16,9.16,0,0,1-1.78,1.28,10.8,10.8,0,0,1-2.1.9,6.4,6.4,0,0,1-1.86.31c-1.57,0-2-.57-2.17-.78a1.65,1.65,0,0,1-.23-.42h1.5a12,12,0,0,0,7.83-2.79Zm26.43-8c.06-.24.11-.48.15-.71h.13Zm2.44-8.51-1.06.25-1.14.28-1,2a6.07,6.07,0,0,0-1.53-1.56l1.12-.53c.77-.39,1.57-.81,2.34-1.22l1.12-.6c.59-.31,1.19-.62,1.77-.9a6,6,0,0,0,.67,2q-.48,0-1,.06A10.4,10.4,0,0,0,107.36,69.44Zm21.15,10a32.18,32.18,0,0,1-1.07,3.24,29.75,29.75,0,0,1-1.5,3.17c-.4.74-.81,1.43-1.22,2a.93.93,0,0,1,0-.13c.29-1,.67-2.08,1.12-3.23a31.22,31.22,0,0,1,1.5-3.25c.38-.72.77-1.38,1.16-2Z",
    transform: "translate(0 0)",
    className: "cjd-stroke",
    style: {
      fill: "#fff"
    }
  }), /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("path", {
    className: "cjd-border",
    d: "M91,66.27a7.49,7.49,0,0,0,.8,1,6.25,6.25,0,0,0,1.12,1,7.06,7.06,0,0,0,1.48.74,7.54,7.54,0,0,0,1.84.41,4.1,4.1,0,0,0,.5,0,7.43,7.43,0,0,0,1.51-.17,14.88,14.88,0,0,0,2.15-.66c.73-.29,1.49-.63,2.25-1s1.54-.79,2.3-1.2l1.12-.6c.76-.4,1.51-.78,2.22-1.13s1.45-.66,2.13-.92a14.32,14.32,0,0,1,2-.58,6.68,6.68,0,0,1,1-.13,3.86,3.86,0,0,0-.8.61,3.59,3.59,0,0,0,0,5.12A4.32,4.32,0,0,0,115,69.87a4.73,4.73,0,0,0,3.15-.57,3.22,3.22,0,0,0,1.24-1.38,3.67,3.67,0,0,0,.38-1.69,4.29,4.29,0,0,0-.42-1.78,5.37,5.37,0,0,0-1.14-1.62,6.1,6.1,0,0,0-1.83-1.19,7.11,7.11,0,0,0-2.45-.56h-.4a9.23,9.23,0,0,0-2.17.28c-.86.21-1.77.49-2.71.82s-1.91.7-2.91,1.08-2,.72-3.1,1a26.79,26.79,0,0,1-3.3.69,14.6,14.6,0,0,1-2,.14A14.27,14.27,0,0,1,95.89,65a5.33,5.33,0,0,1-1.56-.39A5.9,5.9,0,0,1,93.08,64a4.64,4.64,0,0,1-.89-.78,4.55,4.55,0,0,1-.5-.71L91.43,62l-1.62,1.27.06.28A8.15,8.15,0,0,0,91,66.27Z",
    transform: "translate(0 0)",
    style: {
      fill: "#8089a2"
    }
  }), /*#__PURE__*/React.createElement("path", {
    className: "cjd-border",
    d: "M184,89.09a11.56,11.56,0,0,0-4.23-5.31,8.7,8.7,0,0,0-2.82-1.17,6.48,6.48,0,0,0-5,.92,7.92,7.92,0,0,0,0-2.66A5.69,5.69,0,0,0,170.58,78a5.49,5.49,0,0,0-2.64-1.56,7.52,7.52,0,0,0-3.48-.09,3.58,3.58,0,0,0,1.14-1.23,13.65,13.65,0,0,0,.72-1.35,4.79,4.79,0,0,0,.21-3.33,2.14,2.14,0,0,0-2.25-1.35,5,5,0,0,0-3.27,1.2,8.31,8.31,0,0,0-2.37,3.48,4.82,4.82,0,0,0-.33,1.86,4.08,4.08,0,0,0,.33,1.56,2.07,2.07,0,0,0,.9,1,1.85,1.85,0,0,0,1.44.09q3.42-1,4.47.66t-.57,6A18.57,18.57,0,0,1,163.44,88a15.64,15.64,0,0,1-1.8,2.52,7.66,7.66,0,0,1-2.07,1.68,4.69,4.69,0,0,1-2.25.6,1.89,1.89,0,0,1-1.71-.78,3,3,0,0,1-.39-2,3.37,3.37,0,0,0,.78.12,5,5,0,0,0,3.27-1.2,8.35,8.35,0,0,0,2.37-3.42,4.49,4.49,0,0,0,.18-3.27A2.15,2.15,0,0,0,159.6,81a3.74,3.74,0,0,0-1.08.21,5.17,5.17,0,0,0-1.44.78,9.67,9.67,0,0,0-1.62,1.56A13.42,13.42,0,0,0,153.84,86a10.5,10.5,0,0,0-.56,1.18,6.66,6.66,0,0,1-1.9,2.36,5.36,5.36,0,0,1-2.25,1.08,2,2,0,0,1-1.65-.3q-.6-.48-.12-1.62.12-.36.57-1.41t1-2.49q.6-1.44,1.29-3.06l.86-2.09c.75-1.82.93-3.16.54-4s-1.36-1.31-2.94-1.31H148a8.52,8.52,0,0,0,1.32-2.4,4.34,4.34,0,0,0,.24-3.27,2.19,2.19,0,0,0-2.28-1.35,5,5,0,0,0-3.21,1.2,7.9,7.9,0,0,0-2.31,3.42,4.2,4.2,0,0,0-.3,3.27,1.24,1.24,0,0,0,.1.2,50,50,0,0,1-3.75,5.4l-.7.86a13.56,13.56,0,0,0,.45-2.71,8.67,8.67,0,0,0-.54-3.78,5.32,5.32,0,0,0-2.13-2.55,6.66,6.66,0,0,0-3.63-.93,12.91,12.91,0,0,0-8.43,3.09,21.39,21.39,0,0,0-6,8.79,16.48,16.48,0,0,0-1,3.53,6.66,6.66,0,0,1-2,2.47,5.72,5.72,0,0,1-2.25,1.11,1.93,1.93,0,0,1-1.65-.27q-.6-.48-.12-1.68l1.5-3.72q.66-1.56,1.29-3.12l1-2.46q1.62-4,.78-5.85t-4.26-1.89a11.17,11.17,0,0,0-1.17.06,7.63,7.63,0,0,0-1,.18l-1,.24-1.26,2.58h.6c.91,0,1.5.25,1.74.75a2.42,2.42,0,0,1-.12,2c-.21.44-.49,1.14-.87,2.1s-.79,2-1.23,3.06-.87,2.11-1.29,3.09c-.36.83-.63,1.48-.83,2A6.3,6.3,0,0,1,102,89.54a5.42,5.42,0,0,1-2.24,1.09,2,2,0,0,1-1.66-.31c-.4-.31-.44-.85-.11-1.62.15-.36.41-1,.75-1.8s.7-1.74,1.11-2.7.78-1.89,1.14-2.79.63-1.61.84-2.13q1.56-3.78.42-5.7t-4.08-1.92a8.78,8.78,0,0,0-3.49.75,11.3,11.3,0,0,0-3.18,2.07q-.42-2.82-4.5-2.82a11.17,11.17,0,0,0-1.17.06,7.63,7.63,0,0,0-1,.18l-1,.24-1.2,2.58h.6q1.38,0,1.74.75a2.31,2.31,0,0,1-.18,2l-4,9.74A10.14,10.14,0,0,1,79.13,89a12.15,12.15,0,0,1-2.25,1.62,13.39,13.39,0,0,1-2.58,1.11,8.77,8.77,0,0,1-2.61.42,4.91,4.91,0,0,1-4.17-1.77A4.83,4.83,0,0,1,67,85.88h3.84A9.54,9.54,0,0,0,77,83.66,10.39,10.39,0,0,0,79.88,80a7.92,7.92,0,0,0,.78-3.9A4.9,4.9,0,0,0,79.25,73a5,5,0,0,0-3.72-1.29,13.23,13.23,0,0,0-8.43,3A20.61,20.61,0,0,0,61,83.55a11.9,11.9,0,0,0-.9,4.65,7.76,7.76,0,0,0,1,3.81,6.73,6.73,0,0,0,2.73,2.55,9.12,9.12,0,0,0,4.29.93,19.08,19.08,0,0,0,8.52-1.8A12.52,12.52,0,0,0,79.4,91.9a3.83,3.83,0,0,0,.3,1.73q.87,1.86,4.29,1.86a11.56,11.56,0,0,0,2.22-.18l1-.24,1.2-2.58h-.6q-1.38,0-1.74-.72a2.49,2.49,0,0,1,.12-2q.18-.48.75-1.8t1.2-2.91C88.58,84,89,83,89.45,81.92s.78-1.88,1-2.52q.12-.3.21-.6t.21-.6a5.59,5.59,0,0,1,1.89-1.29,4.3,4.3,0,0,1,1.71-.36,1.22,1.22,0,0,1,1.05.54,1.54,1.54,0,0,1,0,1.41l-3.91,9.37a9.71,9.71,0,0,0-.84,3.3,4.45,4.45,0,0,0,.48,2.4A3.38,3.38,0,0,0,92.84,95a6,6,0,0,0,2.49.48A8.4,8.4,0,0,0,98.24,95,12.5,12.5,0,0,0,101,93.51a11.92,11.92,0,0,0,1.68-1.42,3.43,3.43,0,0,0,.45,1.47q1.14,1.92,4.08,1.92a8.4,8.4,0,0,0,2.91-.54,12.09,12.09,0,0,0,2.76-1.44,12.39,12.39,0,0,0,2.34-2.13,7.55,7.55,0,0,0,.6-.77,7.36,7.36,0,0,0,.39,1.4,5.28,5.28,0,0,0,2.1,2.55,6.65,6.65,0,0,0,3.66.93,12.4,12.4,0,0,0,4.42-.81,14.17,14.17,0,0,0,4-2.37,19.47,19.47,0,0,0,3.45-3.75c.38-.53.73-1.09,1.07-1.68a1.6,1.6,0,0,0,1.15-.54,108.42,108.42,0,0,0,8.16-9.84A5,5,0,0,0,146,76l-4.92,11.88a9.76,9.76,0,0,0-.84,3.3,4.63,4.63,0,0,0,.45,2.4A3.14,3.14,0,0,0,142.23,95a5.91,5.91,0,0,0,2.49.48,8.4,8.4,0,0,0,2.91-.54,12.09,12.09,0,0,0,2.76-1.44,12.65,12.65,0,0,0,2.28-2.06,4.86,4.86,0,0,0,.21,1,4.39,4.39,0,0,0,1.53,2.22,4.79,4.79,0,0,0,3,.87,18.49,18.49,0,0,0,4.5-.54,12.92,12.92,0,0,0,4-1.77,12.67,12.67,0,0,0,3.3-3.24,14.43,14.43,0,0,0,1.56-2.84,6,6,0,0,1,3.06-2.32,5.19,5.19,0,0,1,4.92,1.17,7.42,7.42,0,0,1,2.43,3.63,8.6,8.6,0,0,1,.06,4.51A13.94,13.94,0,0,1,179,98.85a17.53,17.53,0,0,1-4.56,4.32,26.17,26.17,0,0,1-6.81,3.15,33,33,0,0,1-9,1.35,101.78,101.78,0,0,1-11.91-.33q-5.55-.51-10.87-1.38c-3.54-.58-7.07-1.23-10.59-1.92s-7.19-1.33-11-1.89-7.87-1-12.15-1.29a124.21,124.21,0,0,0-14-.15,61.39,61.39,0,0,0-12.87,1.86,52.06,52.06,0,0,0-11.13,4.26,52.65,52.65,0,0,0-9.57,6.42,65.8,65.8,0,0,0-8.26,8.34,37,37,0,0,1,10.63-2,27.21,27.21,0,0,1,8.61,1.08,17.16,17.16,0,0,1,6.21,3.3,11.84,11.84,0,0,1,3.42,4.59,25.55,25.55,0,0,1,7-6.06,41.36,41.36,0,0,1,9.36-4,68.22,68.22,0,0,1,11.28-2.25q6.09-.72,12.87-.9t14.14.06q7.35.24,15,.66a63.82,63.82,0,0,0,17.4-1,40,40,0,0,0,12.15-4.56,23.14,23.14,0,0,0,7.38-6.75,18.38,18.38,0,0,0,3.09-7.6A13.51,13.51,0,0,0,184,89.09ZM69.26,80.36q.87-1.56,1.71-2.82a15.08,15.08,0,0,1,1.65-2.1A4.47,4.47,0,0,1,74,74.36q.9-.36,1.26,0a1.6,1.6,0,0,1,.3,1.23,8.14,8.14,0,0,1-.48,2A17.08,17.08,0,0,1,74,80.18a8.05,8.05,0,0,1-1.8,2.58,3.68,3.68,0,0,1-2.34.9l-2.1-.06A26.65,26.65,0,0,1,69.26,80.36Zm62.26-3.22a17.89,17.89,0,0,1-.57,3,34.62,34.62,0,0,1-1.17,3.53,31.91,31.91,0,0,1-1.62,3.44,28.28,28.28,0,0,1-1.8,2.9,11.69,11.69,0,0,1-1.77,2,2.55,2.55,0,0,1-1.59.75,1,1,0,0,1-1-.75,4.58,4.58,0,0,1-.21-2,13.51,13.51,0,0,1,.57-2.9c.32-1.1.72-2.24,1.2-3.44a31.63,31.63,0,0,1,1.62-3.53,26.23,26.23,0,0,1,1.83-3,11.61,11.61,0,0,1,1.83-2.06,2.62,2.62,0,0,1,1.62-.78,1,1,0,0,1,1,.78A5.43,5.43,0,0,1,131.52,77.14Z",
    transform: "translate(0 0)",
    style: {
      fill: "#8089a2"
    }
  }), /*#__PURE__*/React.createElement("path", {
    className: "cjd-border",
    d: "M56.3,100.1a21.85,21.85,0,0,0,2-7.93A22.47,22.47,0,0,0,57,83.66a26.27,26.27,0,0,0-5-8.35,21.44,21.44,0,0,1-3.73-5.89,15.49,15.49,0,0,1-1.15-5.1c.56-.3,1.09-.6,1.58-.9s1-.61,1.45-.88a8,8,0,0,0-.54,2.78A5.38,5.38,0,0,0,51,69.06a6.24,6.24,0,0,0,3.81,1.86,6.9,6.9,0,0,0,1.13.09,13.32,13.32,0,0,0,4.73-1,14.6,14.6,0,0,0,5.81-3.82A13,13,0,0,0,69.41,61a11.24,11.24,0,0,0,.21-5.4A9.6,9.6,0,0,0,67.28,51a10.25,10.25,0,0,0-4.72-2.84,10.93,10.93,0,0,0-3.14-.44,16.44,16.44,0,0,0-3.61.44,26.92,26.92,0,0,0-6.42,2.34,22.93,22.93,0,0,0-5.18,3.66,20.28,20.28,0,0,0-3.72,4.72,17.71,17.71,0,0,0-2,5.12,28.21,28.21,0,0,1-4.53.76l-6.09.6c-2.15.22-4.34.52-6.53.88a34,34,0,0,0-6.3,1.65,20.57,20.57,0,0,0-5.4,3A12,12,0,0,0,6,75.71a10.46,10.46,0,0,0-.89,4.64,12,12,0,0,0,1,4.18,13.85,13.85,0,0,0,2.23,3.56,15.81,15.81,0,0,0,3,2.73,12.92,12.92,0,0,0,3.21,1.69,7.27,7.27,0,0,0,2.35.44,4.49,4.49,0,0,0,.64,0l.16,0,3.2-2.59L19.72,90a5.74,5.74,0,0,1-2.56-1.63,8,8,0,0,1-1.72-3,9.15,9.15,0,0,1,1.18-7.89,16.16,16.16,0,0,1,4-4.15A24.41,24.41,0,0,1,26,70.42a33.37,33.37,0,0,1,6.18-1.76,48.67,48.67,0,0,1,5.91-.73,17.18,17.18,0,0,0,1.06,5.81,23.11,23.11,0,0,0,3.63,6.38,28,28,0,0,1,4,6.91,21.08,21.08,0,0,1,1.49,6.6,16,16,0,0,1-.76,5.93,14.49,14.49,0,0,1-2.73,4.86,14.27,14.27,0,0,1-4.41,3.43,15.57,15.57,0,0,1-5.83,1.62c-.4,0-.8,0-1.19,0a10.36,10.36,0,0,1-3.45-.54,9.45,9.45,0,0,1-3.36-2,8.34,8.34,0,0,1-2.06-3,10,10,0,0,1-.73-3.52A8.74,8.74,0,0,1,24.45,97a8.19,8.19,0,0,1,1.41-2.24,4,4,0,0,0,1,1.94,2.82,2.82,0,0,0,2.11.87,4.28,4.28,0,0,0,1-.12,7.62,7.62,0,0,0,3.26-1.6,9.64,9.64,0,0,0,2.17-2.65,9.47,9.47,0,0,0,1-3,6.13,6.13,0,0,0-.2-2.83,3.49,3.49,0,0,0-1.69-2,3.67,3.67,0,0,0-1.72-.4,6.42,6.42,0,0,0-1.39.17,14.2,14.2,0,0,0-6.58,3.44,17,17,0,0,0-4.13,5.84,17.37,17.37,0,0,0-1.4,6.84,12.85,12.85,0,0,0,1.64,6.5,12,12,0,0,0,5.1,4.69A15.32,15.32,0,0,0,33,113.81c.6,0,1.22,0,1.85-.07a27.74,27.74,0,0,0,9.3-2.16,25.2,25.2,0,0,0,7.32-4.78A21.84,21.84,0,0,0,56.3,100.1ZM48.72,57.7a12.37,12.37,0,0,1,4.55-4.64A10.17,10.17,0,0,1,56,52a11.88,11.88,0,0,1,2.54-.31,7.68,7.68,0,0,1,2,.27,6.46,6.46,0,0,1,3,1.84,6,6,0,0,1,1.43,3,7.61,7.61,0,0,1-.2,3.54,8.5,8.5,0,0,1-2,3.4,9.64,9.64,0,0,1-4,2.51,7.72,7.72,0,0,1-2.52.55,3,3,0,0,1-.68-.08,2.15,2.15,0,0,1-1.49-1.07,3.86,3.86,0,0,1-.39-2.2,7.94,7.94,0,0,1,.71-2.74,9.28,9.28,0,0,1,1.71-2.55,6,6,0,0,1,2.4-1.6l.7-.24-2.29-3.4-.5.44c-.79.72-1.71,1.47-2.73,2.25-.86.67-1.92,1.43-3.15,2.27-.89.6-1.85,1.21-2.86,1.82A10.71,10.71,0,0,1,48.72,57.7Z",
    transform: "translate(0 0)",
    style: {
      fill: "#8089a2"
    }
  }))), /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("path", {
    id: "upperTextArc",
    "data-name": "Patch3 path1",
    d: "M26.93,88.2c3.17-91,132.6-90.47,136.11,0",
    transform: "translate(0 0)",
    className: "cjd-none",
    style: {
      fill: "none"
    }
  }), /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("text", {
    id: "XMLID_694_",
    fontFamily: "Franchise-Bold",
    fontSize: "18.3251px",
    textAnchor: "middle",
    dominantBaseline: "middle",
    className: "cjd-fill"
  }, /*#__PURE__*/React.createElement("textPath", {
    xlinkHref: "#upperTextArc",
    startOffset: "50%",
    style: {},
    fill: "#525a6f"
  }, "WRITE NAME")))), /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("path", {
    d: "M9.91,88.2c5.43,113.14,164.32,113.59,170.15,0",
    transform: "translate(0 0)",
    style: {
      fill: "none"
    },
    className: "cjd-none"
  }), /*#__PURE__*/React.createElement("path", {
    id: "lowerTextArc",
    "data-name": "Patch3 path2",
    d: "M26.7,88.15a68.32,68.32,0,1,0,136.63,0",
    transform: "translate(0 0)",
    style: {
      fill: "none"
    },
    className: "cjd-none"
  }), /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("text", {
    fontFamily: "Franchise-Bold",
    fontSize: "18.3251px",
    textAnchor: "middle",
    dominantBaseline: "middle",
    className: "cjd-fill"
  }, /*#__PURE__*/React.createElement("textPath", {
    xlinkHref: "#lowerTextArc",
    style: {},
    startOffset: "50%",
    fill: "#525a6f"
  }, "WRITE NAME"))))));
}
var ForwardRef = /*#__PURE__*/React.forwardRef(SvgSeniors);
export default __webpack_public_path__ + "static/media/seniors.defcabf801fda71b299fa613886eebf2.svg";
export { ForwardRef as ReactComponent };