import React from 'react';

const StyleIcon = ({ fill }) => {
    return (
        // <svg xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" style={{ fill: "var(--svg-fill-color)", stroke: "var(--svg-stroke-color)"}} height="25px" width="25px" version="1.1" id="Layer_1" viewBox="0 0 512.028 512.028" space="preserve">
        //     <defs>
        //         <style>

        //             {`
        // :root {
        //     --svg-fill-color: none;
        //     --svg-stroke-color: none;
        //   }
        // .cls-1 {
        //   fill: none;
        //   stroke-linecap: round;
        //   stroke-linejoin: round;
        // }`}
        //         </style>
        //     </defs>
        //     <g>
        //         <g>
        //             <g>
        //                 <path d="M436.776,497.792l-63.125-178.731c38.912-33.28,63.68-82.624,63.68-137.728C437.331,81.344,355.987,0,255.997,0     S74.664,81.344,74.664,181.333c0,55.083,24.789,104.427,63.68,137.728L75.261,497.792c-1.451,4.075-0.256,8.661,3.008,11.541     c2.005,1.749,4.523,2.667,7.061,2.667c1.621,0,3.264-0.384,4.779-1.131l79.232-39.616l48,38.421     c2.88,2.304,6.72,2.923,10.219,1.728c3.477-1.237,6.08-4.139,6.869-7.744l14.741-66.347l29.035,68.203     c1.301,3.093,3.968,5.355,7.211,6.165c3.2,0.875,6.677,0.064,9.28-2.005l48-38.421l79.232,39.616     c1.515,0.747,3.136,1.131,4.779,1.131c2.539,0,5.056-0.917,7.061-2.667C437.032,506.453,438.205,501.867,436.776,497.792z      M307.773,332.565l-33.045,7.573c-6.165,0.725-12.395,1.195-18.731,1.195c-88.235,0-160-71.765-160-160s71.765-160,160-160     s160,71.765,160,160C415.997,251.435,370.643,310.997,307.773,332.565z" />
        //                 <path d="M255.997,64c-64.704,0-117.333,52.651-117.333,117.333s52.629,117.333,117.333,117.333s117.333-52.651,117.333-117.333     S320.701,64,255.997,64z M327.507,146.197l-85.333,85.333c-2.091,2.091-4.821,3.115-7.552,3.115s-5.461-1.045-7.552-3.115     l-42.667-42.667c-4.117-4.16-4.117-10.901,0.043-15.083c4.16-4.16,10.923-4.16,15.083,0l35.115,35.115l77.781-77.781     c4.16-4.16,10.923-4.16,15.083,0C331.667,135.275,331.667,142.037,327.507,146.197z" />
        //             </g>
        //         </g>
        //     </g>
        // </svg>
        <svg xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" style={{ fill: "var(--svg-fill-color)", stroke: "var(--svg-stroke-color)"}} height="25px" width="25px" version="1.1" id="Layer_1" viewBox="0 0 512.001 512.001" space="preserve">
         <defs>
                <style>

                    {`
        :root {
            --svg-fill-color: none;
            --svg-stroke-color: none;
          }
        .cls-1 {
          fill: none;
          stroke-linecap: round;
          stroke-linejoin: round;
        }`}
                </style>
            </defs>
        <g>
            <g>
                <path d="M509.865,330.177c-37.138-64.317-131.19-227.23-135.066-233.942c-2.647-4.937-7.916-8.413-14.231-8.413    c-0.007,0-0.014,0-0.021,0H330.23V54.259c0-8.731-7.141-15.927-15.938-15.927H197.708c-8.914,0-15.927,7.272-15.927,15.927v33.563    h-30.327c-0.007,0-0.014,0-0.021,0c-6.069,0-11.22,3.198-13.972,7.963c-2.711,4.695-98.393,170.431-135.325,234.391    c-4.396,7.612-1.788,17.36,5.83,21.758l69.42,40.072c7.622,4.398,17.359,1.784,21.755-5.83    c28.27-48.961,18.24-31.587,36.186-62.674v84.638c0,8.082,6.026,14.743,13.827,15.773v33.828c0,8.796,7.131,15.927,15.927,15.927    h181.838c8.796,0,15.927-7.131,15.927-15.927v-33.826c7.806-1.026,13.838-7.688,13.838-15.774v-84.619    c17.058,29.548,7.133,12.357,36.174,62.654c4.4,7.62,14.141,10.225,21.755,5.83l69.42-40.072    C511.648,347.539,514.264,337.793,509.865,330.177z M79.519,356.457L37.686,332.31l7.198-12.465l41.833,24.147L79.519,356.457z     M284.738,70.185l-28.736,43.612l-28.73-43.612H284.738z M271.927,151.256v240.959h-31.854V151.256l7.07,4.73    c5.411,3.607,12.413,3.547,17.713,0L271.927,151.256z M330.993,441.814H181.009v-17.746c2.458,0,148.294,0,149.984,0V441.814z     M336.537,205.116H314.42c-8.796,0-15.927-7.131-15.927-15.927s7.131-15.927,15.927-15.927h22.117    c8.796,0,15.927,7.131,15.927,15.927S345.333,205.116,336.537,205.116z M432.482,356.457l-7.198-12.465l41.833-24.147    l7.198,12.465L432.482,356.457z"/>
            </g>
        </g>
        </svg>
    );
};

export default StyleIcon;
