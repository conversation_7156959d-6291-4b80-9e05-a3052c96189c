import React from 'react';

const BadgeIcon = ({ fill }) => {
    return (
        <svg
            viewBox="0 -1 20 20"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            height="32px"
            fill="#ffffff">
            <g id="SVGRepo_bgCarrier" stroke-width="0">
            </g>
            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round">
            </g>
            <g id="SVGRepo_iconCarrier">
                <title>
                    hamburger
                </title>
                <desc>
                    Created with Sketch Beta.
                </desc>
                <defs>
                </defs>
                <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" type="MSPage">
                    <g id="Icon-Set" type="MSLayerGroup" transform="translate(-210.000000, -887.000000)" fill="#ffffff">
                        <path d="M229,895 L211,895 C210.448,895 210,895.448 210,896 C210,896.553 210.448,897 211,897 L229,897 C229.552,897 230,896.553 230,896 C230,895.448 229.552,895 229,895 L229,895 Z M229,903 L211,903 C210.448,903 210,903.448 210,904 C210,904.553 210.448,905 211,905 L229,905 C229.552,905 230,904.553 230,904 C230,903.448 229.552,903 229,903 L229,903 Z M211,889 L229,889 C229.552,889 230,888.553 230,888 C230,887.448 229.552,887 229,887 L211,887 C210.448,887 210,887.448 210,888 C210,888.553 210.448,889 211,889 L211,889 Z" id="hamburger" type="MSShapeGroup">
                        </path>
                    </g>
                </g>
            </g>
        </svg>

    );
};

export default BadgeIcon;
