{"name": "easy-jacket", "version": "1.0.0", "description": "", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "axios": "^1.5.0", "call-bind": "^1.0.2", "cheerio": "^1.0.0-rc.12", "classnames": "^2.3.2", "clsx": "^2.0.0", "create-react-app": "^5.0.1", "dompurify": "^3.0.5", "exenv": "^1.2.2", "i": "^0.3.7", "npm": "^10.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropdown": "^1.11.0", "react-modal": "^3.16.1", "react-redux": "^8.1.2", "react-scripts": "^5.0.1", "react-tabs": "^6.0.2", "react-toggle": "^4.1.3", "redux": "^4.2.1", "redux-thunk": "^2.4.2", "saas": "^1.0.0", "sass": "^1.68.0", "save-svg-as-png": "^1.4.17", "select2-react-component": "^6.1.1", "webfontloader": "^1.6.28"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@svgr/webpack": "^8.1.0", "eslint": "^9.4.0", "svg-url-loader": "^8.0.0"}, "main": "index.js"}