import React from 'react';

const BadgeIcon = ({ fill }) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" xlink="http://www.w3.org/1999/xlink" style={{ fill: "var(--svg-fill-color)", stroke: "var(--svg-stroke-color)"}} height="25px" width="25px" version="1.1" id="Layer_1" viewBox="0 0 512.028 512.028" space="preserve">
            <defs>
                <style>

                    {`
        :root {
            --svg-fill-color: none;
            --svg-stroke-color: none;
          }
        .cls-1 {
          fill: none;
          stroke-linecap: round;
          stroke-linejoin: round;
        }`}
                </style>
            </defs>
            <g>
                <g>
                    <g>
                        <path d="M436.776,497.792l-63.125-178.731c38.912-33.28,63.68-82.624,63.68-137.728C437.331,81.344,355.987,0,255.997,0     S74.664,81.344,74.664,181.333c0,55.083,24.789,104.427,63.68,137.728L75.261,497.792c-1.451,4.075-0.256,8.661,3.008,11.541     c2.005,1.749,4.523,2.667,7.061,2.667c1.621,0,3.264-0.384,4.779-1.131l79.232-39.616l48,38.421     c2.88,2.304,6.72,2.923,10.219,1.728c3.477-1.237,6.08-4.139,6.869-7.744l14.741-66.347l29.035,68.203     c1.301,3.093,3.968,5.355,7.211,6.165c3.2,0.875,6.677,0.064,9.28-2.005l48-38.421l79.232,39.616     c1.515,0.747,3.136,1.131,4.779,1.131c2.539,0,5.056-0.917,7.061-2.667C437.032,506.453,438.205,501.867,436.776,497.792z      M307.773,332.565l-33.045,7.573c-6.165,0.725-12.395,1.195-18.731,1.195c-88.235,0-160-71.765-160-160s71.765-160,160-160     s160,71.765,160,160C415.997,251.435,370.643,310.997,307.773,332.565z" />
                        <path d="M255.997,64c-64.704,0-117.333,52.651-117.333,117.333s52.629,117.333,117.333,117.333s117.333-52.651,117.333-117.333     S320.701,64,255.997,64z M327.507,146.197l-85.333,85.333c-2.091,2.091-4.821,3.115-7.552,3.115s-5.461-1.045-7.552-3.115     l-42.667-42.667c-4.117-4.16-4.117-10.901,0.043-15.083c4.16-4.16,10.923-4.16,15.083,0l35.115,35.115l77.781-77.781     c4.16-4.16,10.923-4.16,15.083,0C331.667,135.275,331.667,142.037,327.507,146.197z" />
                    </g>
                </g>
            </g>
        </svg>
    );
};

export default BadgeIcon;
