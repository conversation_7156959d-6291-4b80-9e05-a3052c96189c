import React from 'react';

const BadgeIcon = ({ fill }) => {
    return (
        <svg viewBox="0 -4.89 69.754 69.754" xmlns="http://www.w3.org/2000/svg" fill="#000000">
            <defs>
                <style>

                    {`
        :root {
            --svg-fill-color: none;
            --svg-stroke-color: none;
          }
        `}
                </style>
            </defs>
            <g id="SVGRepo_bgCarrier" stroke-width="0" style={{ fill: "var(--svg-fill-color)", stroke: "var(--svg-stroke-color)"}}></g>
            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" style={{ fill: "var(--svg-fill-color)", stroke: "var(--svg-stroke-color)"}}></g>
            <g id="SVGRepo_iconCarrier" style={{ fill: "var(--svg-fill-color)", stroke: "var(--svg-stroke-color)"}}>
                <g id="Jacket" transform="translate(-218.201 -259.795)" style={{ fill: "var(--svg-fill-color)", stroke: "var(--svg-stroke-color)"}}>
                    <g id="Group_192" data-name="Group 192" style={{ fill: "var(--svg-fill-color)", stroke: "var(--svg-stroke-color)"}}>
                        <path id="Path_144" data-name="Path 144" d="M253.079,319.774h-15c-1.247,0-2.733-.624-2.733-3.6l1.565-39.363a1,1,0,0,1,2,.079l-1.564,39.323c0,1.558.39,1.558.734,1.558h15a1,1,0,1,1,0,2Z" fill="#231f20"></path>
                    </g>
                    <g id="Group_193" data-name="Group 193">
                        <path id="Path_145" data-name="Path 145" d="M228.2,313.005a3.674,3.674,0,0,1-.787-.091l-7.143-1.041a2.365,2.365,0,0,1-1.962-1.425,3.917,3.917,0,0,1,.1-2.1l9.081-40.456a.925.925,0,0,1,.051-.162c2.043-4.949,11.449-6.913,12.888-7.064l5.051-.858a1,1,0,0,1,.61.089,18.747,18.747,0,0,0,6.99,1.792v2a20.1,20.1,0,0,1-7.575-1.857l-4.769.811a.99.99,0,0,1-.164.014c-1.256.087-9.538,2.1-11.151,5.762l-9.06,40.365a5.043,5.043,0,0,0-.157,1.007c.024,0,.151.053.428.115l7.144,1.04c.577.126.827.18,1.116-1.118L237,276.385l1.943.471-8.1,33.425C230.438,312.088,229.551,313.005,228.2,313.005Z" fill="#231f20"></path>
                    </g>
                    <g id="Group_194" data-name="Group 194">
                        <path id="Path_146" data-name="Path 146" d="M253.079,318.774" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width="0.5"></path>
                    </g>
                    <g id="Group_195" data-name="Group 195">
                        <path id="Path_147" data-name="Path 147" d="M253.079,318.774" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width="0.5"></path>
                    </g>
                    <g id="Group_196" data-name="Group 196">
                        <path id="Path_148" data-name="Path 148" d="M268.076,319.774h-15a1,1,0,0,1,0-2h15c.343,0,.732,0,.732-1.6l-1.562-39.284a1,1,0,0,1,2-.079l1.563,39.323C270.808,319.15,269.322,319.774,268.076,319.774Z" fill="#231f20"></path>
                    </g>
                    <g id="Group_197" data-name="Group 197">
                        <path id="Path_149" data-name="Path 149" d="M277.931,313.014c-1.317,0-2.21-.92-2.617-2.753l-8.1-33.4,1.944-.471,8.1,33.425c.293,1.316.543,1.262,1.049,1.15l7.286-1.067a1.062,1.062,0,0,0,.4-.148c-.007-.088-.143-.7-.2-.963l-9.059-40.362c-1.614-3.667-9.9-5.675-11.178-5.762-.033,0-.105-.009-.138-.014l-4.769-.811a20.1,20.1,0,0,1-7.574,1.857v-2a18.643,18.643,0,0,0,6.988-1.792,1.009,1.009,0,0,1,.612-.089l5.051.858c1.438.151,10.845,2.115,12.888,7.063a.959.959,0,0,1,.051.163l9.08,40.454a3.925,3.925,0,0,1,.1,2.1,2.318,2.318,0,0,1-1.89,1.412l-7.287,1.067A3.46,3.46,0,0,1,277.931,313.014Z" fill="#231f20"></path>
                    </g>
                    <g id="Group_198" data-name="Group 198">
                        <path id="Path_150" data-name="Path 150" d="M253.079,318.774" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width="0.5"></path>
                    </g>
                    <g id="Group_199" data-name="Group 199">
                        <path id="Path_151" data-name="Path 151" d="M253.079,318.774" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width="0.5"></path>
                    </g>
                    <g id="Group_200" data-name="Group 200">
                        <rect id="Rectangle_27" data-name="Rectangle 27" width="1" height="54.548" transform="translate(252.579 263.375)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_201" data-name="Group 201">
                        <path id="Path_152" data-name="Path 152" d="M259.834,275.362h-1.741a.5.5,0,0,1,0-1h1.741a.5.5,0,0,1,0,1Z" fill="#231f20"></path>
                    </g>
                    <g id="Group_202" data-name="Group 202">
                        <path id="Path_153" data-name="Path 153" d="M263.17,275.362h-1.74a.5.5,0,0,1,0-1h1.74a.5.5,0,0,1,0,1Z" fill="#231f20"></path>
                    </g>
                    <g id="Group_203" data-name="Group 203">
                        <path id="Path_154" data-name="Path 154" d="M241.927,311.2a.5.5,0,0,1-.13-.983c.065-.02,2.917-1,2.917-8.846a.5.5,0,0,1,1,0c0,8.793-3.515,9.777-3.665,9.814A.515.515,0,0,1,241.927,311.2Z" fill="#231f20"></path>
                    </g>
                    <g id="Group_204" data-name="Group 204">
                        <path id="Path_155" data-name="Path 155" d="M263.587,311.2a.5.5,0,0,1-.121-.015c-.15-.037-3.666-1.021-3.666-9.814a.5.5,0,0,1,1,0c0,7.9,2.889,8.838,2.919,8.847a.5.5,0,0,1,.346.612A.5.5,0,0,1,263.587,311.2Z" fill="#231f20"></path>
                    </g>
                    <g id="Group_205" data-name="Group 205">
                        <rect id="Rectangle_28" data-name="Rectangle 28" width="0.5" height="2.353" transform="translate(239.718 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_206" data-name="Group 206">
                        <rect id="Rectangle_29" data-name="Rectangle 29" width="0.5" height="2.353" transform="translate(237.718 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_207" data-name="Group 207">
                        <rect id="Rectangle_30" data-name="Rectangle 30" width="0.5" height="2.353" transform="translate(241.677 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_208" data-name="Group 208">
                        <rect id="Rectangle_31" data-name="Rectangle 31" width="0.5" height="2.353" transform="translate(243.656 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_209" data-name="Group 209">
                        <rect id="Rectangle_32" data-name="Rectangle 32" width="0.5" height="2.353" transform="translate(245.343 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_210" data-name="Group 210">
                        <rect id="Rectangle_33" data-name="Rectangle 33" width="2.353" height="0.5" transform="matrix(0.179, -0.984, 0.984, 0.179, 221.249, 310.325)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_211" data-name="Group 211">
                        <rect id="Rectangle_34" data-name="Rectangle 34" width="2.353" height="0.5" transform="translate(222.663 310.325) rotate(-79.66)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_212" data-name="Group 212">
                        <rect id="Rectangle_35" data-name="Rectangle 35" width="2.353" height="0.5" transform="translate(224.003 310.52) rotate(-79.649)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_213" data-name="Group 213">
                        <rect id="Rectangle_36" data-name="Rectangle 36" width="2.353" height="0.5" transform="translate(225.346 310.697) rotate(-79.66)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_214" data-name="Group 214">
                        <rect id="Rectangle_37" data-name="Rectangle 37" width="2.353" height="0.5" transform="translate(226.639 310.949) rotate(-79.647)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_215" data-name="Group 215">
                        <rect id="Rectangle_38" data-name="Rectangle 38" width="0.5" height="2.353" transform="translate(247.218 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_216" data-name="Group 216">
                        <rect id="Rectangle_39" data-name="Rectangle 39" width="0.5" height="2.353" transform="translate(249.281 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_217" data-name="Group 217">
                        <rect id="Rectangle_40" data-name="Rectangle 40" width="0.5" height="2.353" transform="translate(251.031 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_218" data-name="Group 218">
                        <rect id="Rectangle_41" data-name="Rectangle 41" width="0.5" height="2.353" transform="translate(254.843 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_219" data-name="Group 219">
                        <rect id="Rectangle_42" data-name="Rectangle 42" width="0.5" height="2.353" transform="translate(256.353 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_220" data-name="Group 220">
                        <rect id="Rectangle_43" data-name="Rectangle 43" width="0.5" height="2.353" transform="translate(258.332 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_221" data-name="Group 221">
                        <rect id="Rectangle_44" data-name="Rectangle 44" width="0.5" height="2.353" transform="translate(260.019 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_222" data-name="Group 222">
                        <rect id="Rectangle_45" data-name="Rectangle 45" width="0.5" height="2.353" transform="translate(261.894 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_223" data-name="Group 223">
                        <rect id="Rectangle_46" data-name="Rectangle 46" width="0.5" height="2.353" transform="translate(263.957 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_224" data-name="Group 224">
                        <rect id="Rectangle_47" data-name="Rectangle 47" width="0.5" height="2.353" transform="translate(265.707 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_225" data-name="Group 225">
                        <rect id="Rectangle_48" data-name="Rectangle 48" width="0.5" height="2.353" transform="translate(267.707 315.57)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_226" data-name="Group 226">
                        <rect id="Rectangle_49" data-name="Rectangle 49" width="33.383" height="1" transform="translate(236.425 315.07)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_227" data-name="Group 227">
                        <rect id="Rectangle_50" data-name="Rectangle 50" width="1" height="10.14" transform="matrix(0.16, -0.987, 0.987, 0.16, 219.587, 307.798)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_228" data-name="Group 228">
                        <rect id="Rectangle_51" data-name="Rectangle 51" width="0.5" height="2.353" transform="translate(283.838 308.163) rotate(-10.353)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_229" data-name="Group 229">
                        <rect id="Rectangle_52" data-name="Rectangle 52" width="0.5" height="2.353" transform="translate(282.424 308.163) rotate(-10.331)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_230" data-name="Group 230">
                        <rect id="Rectangle_53" data-name="Rectangle 53" width="0.5" height="2.353" transform="translate(281.084 308.358) rotate(-10.352)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_231" data-name="Group 231">
                        <rect id="Rectangle_54" data-name="Rectangle 54" width="0.5" height="2.353" transform="translate(279.742 308.535) rotate(-10.353)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_232" data-name="Group 232">
                        <rect id="Rectangle_55" data-name="Rectangle 55" width="0.5" height="2.353" transform="translate(278.448 308.788) rotate(-10.353)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_233" data-name="Group 233">
                        <rect id="Rectangle_56" data-name="Rectangle 56" width="10.14" height="1" transform="translate(276.245 308.497) rotate(-9.217)" fill="#231f20"></rect>
                    </g>
                    <g id="Group_234" data-name="Group 234">
                        <path id="Path_156" data-name="Path 156" d="M255.4,267.341a1.674,1.674,0,0,1-1.193-.495l-.112-.169-1.531-3.86,4.029,1.643a1.687,1.687,0,0,1-1.193,2.881Zm-.419-1.142a.7.7,0,0,0,.9-.06.688.688,0,0,0,.059-.9l-1.6-.635Z" fill="#231f20"></path>
                    </g>
                </g>
            </g>
        </svg>
    );
};

export default BadgeIcon;
